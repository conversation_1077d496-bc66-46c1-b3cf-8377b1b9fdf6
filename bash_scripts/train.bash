cd ~/IsaacLab

# ./isaaclab.sh -p scripts/reinforcement_learning/skrl/train.py \
# --task <PERSON>-<PERSON><PERSON>-<PERSON>ube-Franka-v0 \
# --num_envs 4096 \
# --headless

# ./isaaclab.sh -p scripts/reinforcement_learning/skrl/train.py \
# --task <PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-v0 \
# --num_envs 2048 \
# --headless

# ./isaaclab.sh -p scripts/reinforcement_learning/skrl/train.py \
# --task <PERSON>-<PERSON>ft-Cube-ZLZK-v0 \
# --num_envs 4096 \
# --headless

# ./isaaclab.sh -p scripts/reinforcement_learning/rl_games/train.py \
# --task <PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-Shadow-Vision-Direct-v0 \
# --num_envs 1024 \
# --enable_cameras \
# --headless

./isaaclab.sh -p scripts/reinforcement_learning/skrl/train.py \
--task <PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-Rohand-Direct-v0 \
--num_envs 1 \
# --headless
